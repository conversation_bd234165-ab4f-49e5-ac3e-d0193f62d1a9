<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务条款 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* 导航栏 */
        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand i {
            color: var(--primary);
            font-size: 1.8rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--text-primary) !important;
            background: rgba(99, 102, 241, 0.1);
        }

        /* 卡片样式 */
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 页面标题 */
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 50%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* 内容样式 */
        .content-section {
            margin-bottom: 2rem;
        }

        .content-section h3 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .content-section h4 {
            color: var(--text-primary);
            margin-bottom: 0.8rem;
            font-weight: 500;
        }

        .content-section p {
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .content-section ul {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            padding-left: 1.5rem;
        }

        .content-section li {
            margin-bottom: 0.5rem;
        }

        .highlight {
            background: rgba(99, 102, 241, 0.1);
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary);
            margin: 1rem 0;
        }

        .warning-box {
            background: rgba(245, 158, 11, 0.1);
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--warning);
            margin: 1rem 0;
        }

        .last-updated {
            background: rgba(255, 255, 255, 0.02);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-muted);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i>
                QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item" id="strategyEditorNav" style="display: none;">
                        <a class="nav-link" href="/strategy-editor">
                            <i class="fas fa-code me-1"></i>策略开发
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stock-prediction">
                            <i class="fas fa-brain me-1"></i>AI股票预测
                            <span class="badge bg-warning ms-1">VIP</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-history me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>社区论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showRegister()">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5 pt-4">
        <!-- 页面标题 -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="page-title">
                    <i class="fas fa-file-contract me-3"></i>服务条款
                </h1>
                <p class="page-subtitle">使用QuantTradeX平台前，请仔细阅读并同意以下条款</p>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-body">
                        <div class="last-updated">
                            <i class="fas fa-calendar-alt me-2"></i>
                            最后更新时间：2025年1月27日
                        </div>

                        <div class="content-section">
                            <h3>1. 服务条款的接受</h3>
                            <p>欢迎使用QuantTradeX量化交易平台（以下简称"本平台"或"我们"）。通过访问或使用本平台，您同意受本服务条款（以下简称"条款"）的约束。如果您不同意这些条款，请不要使用本平台。</p>
                        </div>

                        <div class="content-section">
                            <h3>2. 服务描述</h3>
                            <p>QuantTradeX是一个专业的量化交易策略平台，提供以下服务：</p>
                            <ul>
                                <li>量化交易策略开发和回测</li>
                                <li>实时市场数据和分析工具</li>
                                <li>AI驱动的股票预测服务</li>
                                <li>社区论坛和策略分享</li>
                                <li>实盘交易接口和风险管理</li>
                                <li>教育资源和技术支持</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>3. 用户账户和责任</h3>
                            <h4>3.1 账户注册</h4>
                            <ul>
                                <li>您必须提供准确、完整的注册信息</li>
                                <li>您有责任维护账户信息的准确性</li>
                                <li>您必须保护账户密码的安全性</li>
                                <li>一个人只能注册一个账户</li>
                            </ul>

                            <h4>3.2 用户责任</h4>
                            <ul>
                                <li>您对账户下的所有活动负责</li>
                                <li>不得与他人共享账户信息</li>
                                <li>发现账户被盗用时应立即通知我们</li>
                                <li>遵守所有适用的法律法规</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>4. 使用限制和禁止行为</h3>
                            <p>使用本平台时，您不得：</p>
                            <ul>
                                <li>进行任何非法、欺诈或恶意活动</li>
                                <li>干扰或破坏平台的正常运行</li>
                                <li>尝试未经授权访问系统或数据</li>
                                <li>传播恶意软件或病毒</li>
                                <li>侵犯他人的知识产权</li>
                                <li>发布虚假、误导性或有害信息</li>
                                <li>进行市场操纵或内幕交易</li>
                                <li>使用自动化工具恶意访问平台</li>
                            </ul>
                        </div>

                        <div class="warning-box">
                            <h4><i class="fas fa-exclamation-triangle me-2"></i>重要风险提示</h4>
                            <p class="mb-0">量化交易涉及重大财务风险。过往表现不代表未来结果。您应该：</p>
                            <ul class="mb-0 mt-2">
                                <li>充分了解交易风险</li>
                                <li>只投资您能承受损失的资金</li>
                                <li>在做出投资决策前咨询专业顾问</li>
                                <li>定期监控您的投资组合</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>5. 知识产权</h3>
                            <p>本平台的所有内容，包括但不限于：</p>
                            <ul>
                                <li>软件代码和算法</li>
                                <li>数据分析和预测模型</li>
                                <li>用户界面设计</li>
                                <li>文档和教程</li>
                                <li>商标和标识</li>
                            </ul>
                            <p>均受知识产权法保护。未经明确授权，您不得复制、修改、分发或商业使用这些内容。</p>
                        </div>

                        <div class="content-section">
                            <h3>6. 数据和隐私</h3>
                            <p>我们重视您的隐私和数据安全：</p>
                            <ul>
                                <li>我们按照隐私政策处理您的个人信息</li>
                                <li>交易数据经过加密保护</li>
                                <li>我们不会出售您的个人信息</li>
                                <li>您可以随时请求删除您的数据</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>7. 费用和付款</h3>
                            <h4>7.1 服务费用</h4>
                            <ul>
                                <li>基础服务免费提供</li>
                                <li>高级功能需要付费订阅</li>
                                <li>费用标准在网站上明确显示</li>
                                <li>我们保留调整价格的权利</li>
                            </ul>

                            <h4>7.2 退款政策</h4>
                            <ul>
                                <li>订阅费用一般不予退还</li>
                                <li>特殊情况下可申请退款</li>
                                <li>退款申请需在购买后7天内提出</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>8. 免责声明</h3>
                            <p>在法律允许的最大范围内：</p>
                            <ul>
                                <li>我们不保证服务的不间断或无错误</li>
                                <li>我们不对投资损失承担责任</li>
                                <li>我们不保证数据的绝对准确性</li>
                                <li>我们不对第三方内容负责</li>
                                <li>您使用本平台的风险由您自行承担</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>9. 服务终止</h3>
                            <p>我们保留在以下情况下终止或暂停您的账户的权利：</p>
                            <ul>
                                <li>违反本服务条款</li>
                                <li>进行欺诈或非法活动</li>
                                <li>长期不活跃的账户</li>
                                <li>技术或安全原因</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>10. 争议解决</h3>
                            <p>如发生争议：</p>
                            <ul>
                                <li>首先通过友好协商解决</li>
                                <li>协商不成可申请仲裁</li>
                                <li>适用中华人民共和国法律</li>
                                <li>争议由上海市仲裁委员会仲裁</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>11. 条款修改</h3>
                            <p>我们可能会不时修改本服务条款。重大修改将通过以下方式通知您：</p>
                            <ul>
                                <li>网站公告</li>
                                <li>邮件通知</li>
                                <li>应用内通知</li>
                            </ul>
                            <p>继续使用服务即表示您接受修改后的条款。</p>
                        </div>

                        <div class="highlight">
                            <h4><i class="fas fa-info-circle me-2"></i>联系我们</h4>
                            <p class="mb-0">如果您对本服务条款有任何疑问，请联系我们：</p>
                            <ul class="mb-0 mt-2">
                                <li>邮箱：<EMAIL></li>
                                <li>电话：+86 400-888-9999</li>
                                <li>地址：上海市浦东新区陆家嘴金融中心</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="text-center py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-secondary mb-3">&copy; 2025 QuantTradeX. 专业量化交易策略平台</p>
                    <div class="d-flex justify-content-center gap-4 flex-wrap mb-3">
                        <a href="/realtime" class="text-secondary text-decoration-none">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                        <a href="/market-data" class="text-secondary text-decoration-none">
                            <i class="fas fa-chart-area me-1"></i>实时行情
                        </a>
                        <a href="/api/system/status" class="text-secondary text-decoration-none">
                            <i class="fas fa-server me-1"></i>系统状态
                        </a>
                        <a href="/api-docs" class="text-secondary text-decoration-none">
                            <i class="fas fa-book me-1"></i>API文档
                        </a>
                        <a href="/contact" class="text-secondary text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a>
                        <a href="/privacy" class="text-secondary text-decoration-none">
                            <i class="fas fa-shield-alt me-1"></i>隐私政策
                        </a>
                        <a href="/terms" class="text-secondary text-decoration-none">
                            <i class="fas fa-file-contract me-1"></i>服务条款
                        </a>
                        <a href="/help" class="text-secondary text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i>帮助中心
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 引入统一登录模态框 -->
    {% include 'components/login_modal.html' %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/user-auth.js') }}"></script>
</body>
</html>
