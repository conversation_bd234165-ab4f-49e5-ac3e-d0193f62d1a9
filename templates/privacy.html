<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* 导航栏 */
        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand i {
            color: var(--primary);
            font-size: 1.8rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--text-primary) !important;
            background: rgba(99, 102, 241, 0.1);
        }

        /* 卡片样式 */
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 页面标题 */
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 50%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* 内容样式 */
        .content-section {
            margin-bottom: 2rem;
        }

        .content-section h3 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .content-section h4 {
            color: var(--text-primary);
            margin-bottom: 0.8rem;
            font-weight: 500;
        }

        .content-section p {
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .content-section ul {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            padding-left: 1.5rem;
        }

        .content-section li {
            margin-bottom: 0.5rem;
        }

        .highlight {
            background: rgba(99, 102, 241, 0.1);
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary);
            margin: 1rem 0;
        }

        .last-updated {
            background: rgba(255, 255, 255, 0.02);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-muted);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i>
                QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item" id="strategyEditorNav" style="display: none;">
                        <a class="nav-link" href="/strategy-editor">
                            <i class="fas fa-code me-1"></i>策略开发
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stock-prediction">
                            <i class="fas fa-brain me-1"></i>AI股票预测
                            <span class="badge bg-warning ms-1">VIP</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-history me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>社区论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showRegister()">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5 pt-4">
        <!-- 页面标题 -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="page-title">
                    <i class="fas fa-shield-alt me-3"></i>隐私政策
                </h1>
                <p class="page-subtitle">我们致力于保护您的个人信息和隐私安全</p>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-body">
                        <div class="last-updated">
                            <i class="fas fa-calendar-alt me-2"></i>
                            最后更新时间：2025年1月27日
                        </div>

                        <div class="content-section">
                            <h3>1. 信息收集</h3>
                            <p>我们可能收集以下类型的信息：</p>
                            <ul>
                                <li><strong>个人身份信息：</strong>姓名、邮箱地址、电话号码等</li>
                                <li><strong>账户信息：</strong>用户名、密码、交易偏好设置</li>
                                <li><strong>交易数据：</strong>策略配置、回测记录、交易历史</li>
                                <li><strong>技术信息：</strong>IP地址、浏览器类型、设备信息</li>
                                <li><strong>使用数据：</strong>页面访问记录、功能使用统计</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>2. 信息使用</h3>
                            <p>我们使用收集的信息用于：</p>
                            <ul>
                                <li>提供和改进我们的量化交易服务</li>
                                <li>处理您的交易请求和账户管理</li>
                                <li>发送重要的服务通知和更新</li>
                                <li>进行安全监控和欺诈防护</li>
                                <li>分析用户行为以优化平台功能</li>
                                <li>遵守法律法规要求</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>3. 信息共享</h3>
                            <p>我们不会出售、交易或转让您的个人信息给第三方，除非：</p>
                            <ul>
                                <li>获得您的明确同意</li>
                                <li>为提供服务所必需的合作伙伴（如支付处理商）</li>
                                <li>法律要求或政府机关的合法要求</li>
                                <li>保护我们的权利、财产或安全</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>4. 数据安全</h3>
                            <p>我们采用多层安全措施保护您的信息：</p>
                            <ul>
                                <li><strong>加密传输：</strong>使用SSL/TLS加密所有数据传输</li>
                                <li><strong>安全存储：</strong>敏感数据采用高级加密算法存储</li>
                                <li><strong>访问控制：</strong>严格限制员工对个人数据的访问</li>
                                <li><strong>定期审计：</strong>定期进行安全评估和漏洞扫描</li>
                                <li><strong>备份恢复：</strong>建立完善的数据备份和恢复机制</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>5. Cookie使用</h3>
                            <p>我们使用Cookie和类似技术来：</p>
                            <ul>
                                <li>记住您的登录状态和偏好设置</li>
                                <li>分析网站流量和用户行为</li>
                                <li>提供个性化的用户体验</li>
                                <li>防止欺诈和提高安全性</li>
                            </ul>
                            <p>您可以通过浏览器设置管理Cookie偏好。</p>
                        </div>

                        <div class="content-section">
                            <h3>6. 您的权利</h3>
                            <p>您对个人信息享有以下权利：</p>
                            <ul>
                                <li><strong>访问权：</strong>查看我们持有的您的个人信息</li>
                                <li><strong>更正权：</strong>更新或修正不准确的信息</li>
                                <li><strong>删除权：</strong>要求删除您的个人信息</li>
                                <li><strong>限制权：</strong>限制我们处理您的信息</li>
                                <li><strong>携带权：</strong>以结构化格式获取您的数据</li>
                                <li><strong>反对权：</strong>反对某些类型的数据处理</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>7. 数据保留</h3>
                            <p>我们仅在必要期间保留您的个人信息：</p>
                            <ul>
                                <li>账户信息：账户存续期间及法律要求的期限</li>
                                <li>交易记录：根据金融监管要求保留相应期限</li>
                                <li>日志数据：通常保留12个月</li>
                                <li>营销数据：直到您取消订阅或要求删除</li>
                            </ul>
                        </div>

                        <div class="content-section">
                            <h3>8. 国际传输</h3>
                            <p>您的信息可能被传输到您所在国家/地区以外的地方进行处理。我们确保此类传输符合适用的数据保护法律，并采取适当的保护措施。</p>
                        </div>

                        <div class="content-section">
                            <h3>9. 未成年人保护</h3>
                            <p>我们的服务不面向18岁以下的未成年人。我们不会故意收集未成年人的个人信息。如果我们发现收集了未成年人的信息，将立即删除。</p>
                        </div>

                        <div class="content-section">
                            <h3>10. 政策更新</h3>
                            <p>我们可能会不时更新本隐私政策。重大变更将通过网站通知或邮件形式告知您。继续使用我们的服务即表示您接受更新后的政策。</p>
                        </div>

                        <div class="highlight">
                            <h4><i class="fas fa-info-circle me-2"></i>联系我们</h4>
                            <p class="mb-0">如果您对本隐私政策有任何疑问或需要行使您的权利，请通过以下方式联系我们：</p>
                            <ul class="mb-0 mt-2">
                                <li>邮箱：<EMAIL></li>
                                <li>电话：+86 400-888-9999</li>
                                <li>地址：上海市浦东新区陆家嘴金融中心</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="text-center py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-secondary mb-3">&copy; 2025 QuantTradeX. 专业量化交易策略平台</p>
                    <div class="d-flex justify-content-center gap-4 flex-wrap mb-3">
                        <a href="/realtime" class="text-secondary text-decoration-none">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                        <a href="/market-data" class="text-secondary text-decoration-none">
                            <i class="fas fa-chart-area me-1"></i>实时行情
                        </a>
                        <a href="/api/system/status" class="text-secondary text-decoration-none">
                            <i class="fas fa-server me-1"></i>系统状态
                        </a>
                        <a href="/api-docs" class="text-secondary text-decoration-none">
                            <i class="fas fa-book me-1"></i>API文档
                        </a>
                        <a href="/contact" class="text-secondary text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a>
                        <a href="/privacy" class="text-secondary text-decoration-none">
                            <i class="fas fa-shield-alt me-1"></i>隐私政策
                        </a>
                        <a href="/terms" class="text-secondary text-decoration-none">
                            <i class="fas fa-file-contract me-1"></i>服务条款
                        </a>
                        <a href="/help" class="text-secondary text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i>帮助中心
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 引入统一登录模态框 -->
    {% include 'components/login_modal.html' %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/user-auth.js') }}"></script>
</body>
</html>
