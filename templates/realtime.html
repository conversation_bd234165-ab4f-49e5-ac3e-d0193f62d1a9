<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时数据中心 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary) !important;
        }

        .container {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
        }

        .card-header {
            background: rgba(99, 102, 241, 0.1);
            border-bottom: 1px solid var(--glass-border);
            border-radius: 16px 16px 0 0 !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .connection-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }

        .connected {
            background: var(--success);
        }

        .disconnected {
            background: var(--danger);
        }

        .connecting {
            background: var(--warning);
        }

        .market-ticker {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .market-ticker:hover {
            background: rgba(0, 0, 0, 0.4);
            transform: translateY(-2px);
        }

        .price-up {
            color: var(--success);
        }

        .price-down {
            color: var(--danger);
        }

        .price-neutral {
            color: var(--text-secondary);
        }

        .watchlist-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .watchlist-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateX(5px);
        }

        .watchlist-item.active {
            border-color: var(--primary);
            background: rgba(99, 102, 241, 0.1);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-select option {
            background: var(--dark-card);
            color: var(--text-primary);
            padding: 0.5rem;
        }

        .form-select option:hover {
            background: var(--primary);
            color: white;
        }

        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            padding: 1rem;
            color: white;
            text-align: center;
            margin-bottom: 1rem;
        }

        .stats-number {
            font-size: 1.5rem;
            font-weight: 800;
            margin-bottom: 0.25rem;
        }

        .stats-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .live-indicator {
            background: var(--danger);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategy-editor">
                            <i class="fas fa-code me-1"></i>策略开发
                        </a>
                    </li>
                    <li class="nav-item" data-feature="stock-prediction" style="display: none;">
                        <a class="nav-link" href="/stock-prediction">
                            <i class="fas fa-brain me-1"></i>AI股票预测
                            <span class="badge bg-warning ms-1">VIP</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-history me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/realtime">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>社区论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="nav-link">
                            <span id="connectionIndicator" class="connection-indicator disconnected"></span>
                            <span id="connectionText">未连接</span>
                        </span>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>账户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                            <li><a class="dropdown-item" href="/security">安全设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- 页面标题 -->
        <div class="text-center mb-4">
            <h1 class="display-6 mb-3">
                <i class="fas fa-broadcast-tower text-primary me-3"></i>
                实时数据中心
                <span class="live-indicator ms-2">LIVE</span>
            </h1>
            <p class="lead text-secondary">实时市场数据流，WebSocket推送技术</p>
        </div>

        <div class="row">
            <!-- 左侧：自选股和控制面板 -->
            <div class="col-lg-4">
                <!-- 连接状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-wifi me-2"></i>连接状态
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <div id="connectionStatus" class="mb-3">
                            <span id="statusIcon" class="connection-indicator disconnected"></span>
                            <span id="statusText">未连接</span>
                        </div>
                        <button class="btn btn-primary btn-sm me-2" onclick="connectWebSocket()">
                            <i class="fas fa-plug me-1"></i>连接
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="disconnectWebSocket()">
                            <i class="fas fa-unlink me-1"></i>断开
                        </button>
                    </div>
                </div>

                <!-- 添加自选股 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-plus me-2"></i>添加自选股
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">交易品种代码</label>
                            <input type="text" class="form-control" id="symbolInput" placeholder="股票: AAPL, GOOGL | 加密货币: BTC, ETH">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">市场类型</label>
                            <select class="form-select" id="marketType">
                                <option value="stock">美股市场</option>
                                <option value="crypto">加密货币</option>
                            </select>
                        </div>
                        <button class="btn btn-primary w-100" onclick="addToWatchlist()">
                            <i class="fas fa-star me-1"></i>添加到自选
                        </button>
                    </div>
                </div>

                <!-- 自选股列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-star me-2"></i>我的自选股
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="watchlistContainer">
                            <p class="text-muted text-center">暂无自选股</p>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>服务统计
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="stats-number" id="totalConnections">0</div>
                            <div class="stats-label">在线用户</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number" id="totalSubscriptions">0</div>
                            <div class="stats-label">活跃订阅</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number" id="uniqueSymbols">0</div>
                            <div class="stats-label">监控品种</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：实时数据显示 -->
            <div class="col-lg-8">
                <!-- 市场概览 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>实时行情
                            <span class="badge bg-success ms-2" id="dataStatusBadge">
                                <i class="fas fa-check-circle me-1"></i>真实数据
                            </span>
                        </h6>
                        <div class="text-end">
                            <small class="text-muted d-block">
                                最后更新: <span id="lastUpdate">--</span>
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-database me-1"></i>数据源: CoinGecko, Yahoo Finance
                            </small>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="marketDataContainer">
                            <div class="text-center py-5">
                                <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">暂无数据</h5>
                                <p class="text-muted">请先连接WebSocket并添加交易品种</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能测试 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tools me-2"></i>功能测试
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-outline-primary w-100" onclick="testConnection()">
                                    <i class="fas fa-heartbeat me-1"></i>测试连接
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-outline-success w-100" onclick="getStats()">
                                    <i class="fas fa-chart-bar me-1"></i>获取统计
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-outline-info w-100" onclick="addDemoStocks()">
                                    <i class="fas fa-plus-circle me-1"></i>添加演示股票
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="/realtime_test" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-flask me-1"></i>高级测试
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/user-auth.js') }}"></script>
    <script>
        let socket = null;
        let watchlist = JSON.parse(localStorage.getItem('quanttradex_watchlist') || '[]');
        let marketData = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查用户登录状态和会员权限
            checkLoginStatus();
            updateWatchlistDisplay();
            connectWebSocket();
        });

        // 连接WebSocket
        function connectWebSocket() {
            if (socket && socket.connected) {
                showNotification('已经连接', 'warning');
                return;
            }

            updateConnectionStatus('connecting');
            socket = io();

            socket.on('connect', function() {
                updateConnectionStatus('connected');
                showNotification('WebSocket连接成功', 'success');

                // 自动订阅自选股
                watchlist.forEach(item => {
                    socket.emit('subscribe', {
                        symbol: item.symbol,
                        type: item.type
                    });
                });
            });

            socket.on('disconnect', function() {
                updateConnectionStatus('disconnected');
                showNotification('WebSocket连接断开', 'error');
            });

            socket.on('market_data', function(data) {
                updateMarketData(data);
            });

            socket.on('stats', function(data) {
                updateStats(data.data);
            });

            socket.on('error', function(data) {
                showNotification(`错误: ${data.message}`, 'error');
            });
        }

        // 断开WebSocket
        function disconnectWebSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
                updateConnectionStatus('disconnected');
                showNotification('已断开连接', 'info');
            }
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            const indicator = document.getElementById('connectionIndicator');
            const text = document.getElementById('connectionText');
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');

            indicator.className = `connection-indicator ${status}`;
            statusIcon.className = `connection-indicator ${status}`;

            switch (status) {
                case 'connected':
                    text.textContent = '已连接';
                    statusText.textContent = '已连接';
                    break;
                case 'connecting':
                    text.textContent = '连接中...';
                    statusText.textContent = '连接中...';
                    break;
                case 'disconnected':
                    text.textContent = '未连接';
                    statusText.textContent = '未连接';
                    break;
            }
        }

        // 添加到自选股
        function addToWatchlist() {
            const symbol = document.getElementById('symbolInput').value.trim().toUpperCase();
            const type = document.getElementById('marketType').value;

            if (!symbol) {
                showNotification('请输入交易品种代码', 'error');
                return;
            }

            // 检查是否已存在
            if (watchlist.some(item => item.symbol === symbol && item.type === type)) {
                showNotification('该品种已在自选中', 'warning');
                return;
            }

            // 添加到自选股
            watchlist.push({ symbol, type });
            localStorage.setItem('quanttradex_watchlist', JSON.stringify(watchlist));
            updateWatchlistDisplay();

            // 如果已连接，立即订阅
            if (socket && socket.connected) {
                socket.emit('subscribe', { symbol, type });
            }

            // 清空输入框
            document.getElementById('symbolInput').value = '';
            showNotification(`已添加 ${symbol} 到自选股`, 'success');
        }

        // 更新自选股显示
        function updateWatchlistDisplay() {
            const container = document.getElementById('watchlistContainer');

            if (watchlist.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">暂无自选股</p>';
                return;
            }

            container.innerHTML = watchlist.map((item, index) => `
                <div class="watchlist-item" onclick="selectWatchlistItem(${index})">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${item.symbol}</strong>
                            <small class="text-muted ms-2">${item.type}</small>
                        </div>
                        <button class="btn btn-outline-danger btn-sm" onclick="event.stopPropagation(); removeFromWatchlist(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 从自选股移除
        function removeFromWatchlist(index) {
            const item = watchlist[index];

            // 取消订阅
            if (socket && socket.connected) {
                socket.emit('unsubscribe', {
                    symbol: item.symbol,
                    type: item.type
                });
            }

            // 从列表移除
            watchlist.splice(index, 1);
            localStorage.setItem('quanttradex_watchlist', JSON.stringify(watchlist));
            updateWatchlistDisplay();

            // 从市场数据中移除
            delete marketData[item.symbol];
            updateMarketDataDisplay();

            showNotification(`已移除 ${item.symbol}`, 'info');
        }

        // 更新市场数据
        function updateMarketData(data) {
            marketData[data.symbol] = data;
            updateMarketDataDisplay();
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        // 更新市场数据显示
        function updateMarketDataDisplay() {
            const container = document.getElementById('marketDataContainer');

            if (Object.keys(marketData).length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无数据</h5>
                        <p class="text-muted">请先连接WebSocket并添加交易品种</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = Object.values(marketData).map(item => {
                const changeClass = item.data.change > 0 ? 'price-up' :
                                   item.data.change < 0 ? 'price-down' : 'price-neutral';
                const changeIcon = item.data.change > 0 ? 'fa-arrow-up' :
                                  item.data.change < 0 ? 'fa-arrow-down' : 'fa-minus';

                // 数据源标识
                const sourceIcon = item.data.source === 'SIMULATED_DATA' ?
                    '<i class="fas fa-flask text-warning" title="模拟数据"></i>' :
                    '<i class="fas fa-check-circle text-success" title="真实数据"></i>';

                const sourceText = item.data.source || 'Unknown';
                const isRealData = item.data.source !== 'SIMULATED_DATA';

                return `
                    <div class="market-ticker pulse ${isRealData ? '' : 'border-warning'}">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <h6 class="mb-0">
                                    ${item.symbol} ${sourceIcon}
                                </h6>
                                <small class="text-muted">${item.type}</small>
                            </div>
                            <div class="col-md-3">
                                <div class="h5 mb-0 ${changeClass}">
                                    $${item.data.price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                                </div>
                                <small class="text-muted">${sourceText}</small>
                            </div>
                            <div class="col-md-3">
                                <div class="${changeClass}">
                                    <i class="fas ${changeIcon} me-1"></i>
                                    ${item.data.change.toFixed(2)} (${item.data.change_percent.toFixed(2)}%)
                                </div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">
                                    成交量<br>${item.data.volume.toLocaleString()}
                                </small>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">
                                    ${new Date(item.timestamp).toLocaleTimeString()}
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 更新统计信息
        function updateStats(stats) {
            document.getElementById('totalConnections').textContent = stats.total_connections;
            document.getElementById('totalSubscriptions').textContent = stats.total_subscriptions;
            document.getElementById('uniqueSymbols').textContent = stats.unique_symbols;
        }

        // 测试连接
        function testConnection() {
            if (socket && socket.connected) {
                socket.emit('ping');
                showNotification('发送心跳测试', 'info');
            } else {
                showNotification('请先连接WebSocket', 'error');
            }
        }

        // 获取统计
        function getStats() {
            if (socket && socket.connected) {
                socket.emit('get_stats');
            } else {
                showNotification('请先连接WebSocket', 'error');
            }
        }

        // 添加演示股票
        function addDemoStocks() {
            const demoStocks = [
                { symbol: 'AAPL', type: 'stock' },
                { symbol: 'GOOGL', type: 'stock' },
                { symbol: 'TSLA', type: 'stock' },
                { symbol: 'BTC', type: 'crypto' }
            ];

            demoStocks.forEach(stock => {
                if (!watchlist.some(item => item.symbol === stock.symbol && item.type === stock.type)) {
                    watchlist.push(stock);

                    if (socket && socket.connected) {
                        socket.emit('subscribe', stock);
                    }
                }
            });

            localStorage.setItem('quanttradex_watchlist', JSON.stringify(watchlist));
            updateWatchlistDisplay();
            showNotification('已添加演示股票', 'success');
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST'
                });

                if (response.ok) {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (socket) {
                socket.disconnect();
            }
        });
    </script>
</body>
</html>
