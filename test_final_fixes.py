#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终修复验证测试
验证所有用户反馈问题的修复效果
"""

import requests
import time

def test_all_pages_navigation():
    """测试所有页面的导航栏一致性"""
    base_url = "http://127.0.0.1:5001"
    
    print("🔍 测试所有页面导航栏一致性")
    print("=" * 60)
    
    # 创建会话并登录
    session = requests.Session()
    login_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        if not result.get('success'):
            print("   ❌ 登录失败，无法测试")
            return False
        print("   ✅ 登录成功")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return False
    
    # 测试页面列表
    test_pages = [
        ('/', '首页'),
        ('/strategy-editor', '策略编辑器'),
        ('/realtime', '实时数据'),
        ('/market-data', '实时行情')
    ]
    
    # 必须包含的导航元素
    required_nav_elements = [
        'fas fa-home',      # 首页
        'fas fa-tachometer-alt',  # 仪表板
        'fas fa-store',     # 策略市场
        'fas fa-code',      # 策略开发
        'fas fa-brain',     # AI预测
        'fas fa-history',   # 基础回测
        'fas fa-rocket',    # 高级回测
        'fas fa-broadcast-tower',  # 实时数据
        'fas fa-comments'   # 论坛
    ]
    
    all_consistent = True
    for url, name in test_pages:
        try:
            response = session.get(f"{base_url}{url}", timeout=10)
            if response.status_code == 200:
                content = response.text
                missing_elements = []
                
                for element in required_nav_elements:
                    if element not in content:
                        missing_elements.append(element)
                
                if not missing_elements:
                    print(f"   ✅ {name}页面导航栏完整")
                else:
                    print(f"   ❌ {name}页面缺少导航元素: {missing_elements}")
                    all_consistent = False
                    
                # 检查用户认证脚本
                if 'user-auth.js' in content:
                    print(f"   ✅ {name}页面包含用户认证脚本")
                else:
                    print(f"   ❌ {name}页面缺少用户认证脚本")
                    all_consistent = False
                    
            else:
                print(f"   ❌ {name}页面访问失败 (状态码: {response.status_code})")
                all_consistent = False
        except Exception as e:
            print(f"   ❌ {name}页面访问异常: {e}")
            all_consistent = False
    
    return all_consistent

def test_strategy_editor_functionality():
    """测试策略编辑器功能"""
    base_url = "http://127.0.0.1:5001"
    
    print("\n🔍 测试策略编辑器功能")
    print("=" * 60)
    
    session = requests.Session()
    login_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        if not result.get('success'):
            print("   ❌ 登录失败")
            return False
        print("   ✅ 登录成功")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return False
    
    # 测试策略编辑器页面
    try:
        response = session.get(f"{base_url}/strategy-editor", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查关键功能元素
            checks = [
                ('CodeMirror', 'CodeMirror编辑器'),
                ('console-output', '控制台输出'),
                ('runStrategy', '运行策略功能'),
                ('saveStrategy', '保存策略功能'),
                ('loadTemplate', '模板加载功能'),
                ('checkLoginStatus', '登录状态检查'),
                ('height: 600px', '编辑器高度设置'),
                ('background: #000000', '黑色背景设置'),
                ('font-size: 14px', '字体大小设置')
            ]
            
            all_functions_work = True
            for check, description in checks:
                if check in content:
                    print(f"   ✅ {description}存在")
                else:
                    print(f"   ❌ {description}缺失")
                    all_functions_work = False
            
            return all_functions_work
        else:
            print(f"   ❌ 策略编辑器页面访问失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"   ❌ 策略编辑器页面访问异常: {e}")
        return False

def test_user_auth_consistency():
    """测试用户认证一致性"""
    base_url = "http://127.0.0.1:5001"
    
    print("\n🔍 测试用户认证一致性")
    print("=" * 60)
    
    session = requests.Session()
    login_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        if not result.get('success'):
            print("   ❌ 登录失败")
            return False
        print("   ✅ 登录成功")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return False
    
    # 测试不同页面的用户状态API一致性
    test_pages = ['/strategy-editor', '/realtime', '/market-data']
    
    all_consistent = True
    for page in test_pages:
        try:
            # 访问页面
            response = session.get(f"{base_url}{page}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {page}页面访问正常")
                
                # 测试用户状态API
                auth_response = session.get(f"{base_url}/auth/check", timeout=5)
                if auth_response.status_code == 200:
                    auth_data = auth_response.json()
                    if auth_data.get('authenticated'):
                        user = auth_data.get('user', {})
                        print(f"   ✅ {page}页面用户状态正常: {user.get('username')}")
                    else:
                        print(f"   ❌ {page}页面用户状态异常: 未认证")
                        all_consistent = False
                else:
                    print(f"   ❌ {page}页面用户状态API失败")
                    all_consistent = False
            else:
                print(f"   ❌ {page}页面访问失败")
                all_consistent = False
        except Exception as e:
            print(f"   ❌ {page}页面测试异常: {e}")
            all_consistent = False
    
    return all_consistent

def test_ui_improvements():
    """测试UI改进"""
    base_url = "http://127.0.0.1:5001"
    
    print("\n🔍 测试UI改进")
    print("=" * 60)
    
    session = requests.Session()
    login_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        if not result.get('success'):
            print("   ❌ 登录失败")
            return False
        print("   ✅ 登录成功")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return False
    
    # 测试实时行情页面UI改进
    try:
        response = session.get(f"{base_url}/market-data", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            ui_checks = [
                ('--text-primary: #f8fafc', '文字颜色对比度'),
                ('background: var(--glass-bg)', '背景透明度'),
                ('navbar navbar-expand-lg', '导航栏存在'),
                ('fas fa-chart-area', '实时行情图标'),
                ('user-auth.js', '用户认证脚本')
            ]
            
            all_ui_good = True
            for check, description in ui_checks:
                if check in content:
                    print(f"   ✅ 实时行情页面{description}正常")
                else:
                    print(f"   ❌ 实时行情页面{description}缺失")
                    all_ui_good = False
            
            return all_ui_good
        else:
            print(f"   ❌ 实时行情页面访问失败")
            return False
    except Exception as e:
        print(f"   ❌ 实时行情页面测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 QuantTradeX 最终修复验证测试")
    print("请确保应用正在运行在 http://127.0.0.1:5001")
    print()
    
    # 执行所有测试
    tests = [
        ("导航栏一致性", test_all_pages_navigation),
        ("策略编辑器功能", test_strategy_editor_functionality),
        ("用户认证一致性", test_user_auth_consistency),
        ("UI改进", test_ui_improvements)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 最终测试结果总结:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有修复验证测试通过！")
        print("\n✨ 修复完成的问题:")
        print("   1. ✅ 实时数据页面导航栏已统一")
        print("   2. ✅ 实时行情页面导航栏已统一，UI对比度已改善")
        print("   3. ✅ 策略编辑器UI已恢复：大尺寸、黑色背景、大字体")
        print("   4. ✅ 策略编辑器功能已修复：API端点统一")
        print("   5. ✅ 所有页面会员状态同步正常")
        
        print("\n🔧 技术改进:")
        print("   - 统一了所有页面的导航栏结构和图标")
        print("   - 修复了API端点不一致问题")
        print("   - 改善了文字和背景的对比度")
        print("   - 恢复了策略编辑器的专业外观")
        print("   - 确保了用户认证脚本在所有页面正确加载")
        
    else:
        print("⚠️  部分修复需要进一步检查。")
    
    return all_passed

if __name__ == "__main__":
    main()
