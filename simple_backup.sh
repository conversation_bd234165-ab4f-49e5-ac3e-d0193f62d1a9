#!/bin/bash
# QuantTradeX 简化备份脚本
# 创建时间: 2025-05-31

# 配置
PROJECT_DIR="/www/wwwroot/www.gdpp.com"
BACKUP_BASE_DIR="/appweb/www/backup"
BACKUP_DIR="$BACKUP_BASE_DIR/quanttradex_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/tmp/backup_$(date +%Y%m%d_%H%M%S).log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理
error_exit() {
    log "❌ 错误: $1"
    exit 1
}

log "🚀 开始 QuantTradeX 简化备份..."
log "备份目录: $BACKUP_DIR"

# 1. 创建备份目录
log "📁 创建备份目录..."
mkdir -p "$BACKUP_DIR"/{project,database,config} || error_exit "创建备份目录失败"

# 2. 备份项目文件
log "📦 备份项目文件..."
cd "$PROJECT_DIR" || error_exit "无法进入项目目录"

# 创建项目文件压缩包（排除不必要文件）
tar --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='*.log' \
    --exclude='venv' \
    --exclude='.git' \
    -czf "$BACKUP_DIR/project/quanttradex_project.tar.gz" . || error_exit "项目文件备份失败"

log "✅ 项目文件备份完成"

# 3. 备份PostgreSQL数据库
log "🗄️ 备份PostgreSQL数据库..."
if command -v pg_dump >/dev/null 2>&1; then
    sudo -u postgres pg_dump quanttradex > "$BACKUP_DIR/database/quanttradex_$(date +%Y%m%d_%H%M%S).sql" || error_exit "数据库备份失败"
    gzip "$BACKUP_DIR/database/quanttradex_$(date +%Y%m%d_%H%M%S).sql"
    log "✅ PostgreSQL数据库备份完成"
else
    log "⚠️ 未找到pg_dump命令，跳过数据库备份"
fi

# 4. 备份Redis数据
log "📦 备份Redis数据..."
if command -v redis-cli >/dev/null 2>&1; then
    redis-cli BGSAVE >/dev/null
    sleep 3
    if [ -f "/var/lib/redis/dump.rdb" ]; then
        cp "/var/lib/redis/dump.rdb" "$BACKUP_DIR/database/redis_$(date +%Y%m%d_%H%M%S).rdb"
        log "✅ Redis数据备份完成"
    else
        log "⚠️ 未找到Redis数据文件"
    fi
else
    log "⚠️ 未找到redis-cli命令，跳过Redis备份"
fi

# 5. 备份配置文件
log "⚙️ 备份配置文件..."
# Nginx配置
if [ -f "/etc/nginx/sites-available/www.gdpp.com" ]; then
    cp "/etc/nginx/sites-available/www.gdpp.com" "$BACKUP_DIR/config/nginx_gdpp.conf"
fi

# 系统服务配置
if [ -f "/etc/systemd/system/quanttradex.service" ]; then
    cp "/etc/systemd/system/quanttradex.service" "$BACKUP_DIR/config/"
fi

log "✅ 配置文件备份完成"

# 6. 生成备份信息
log "📝 生成备份信息..."
cat > "$BACKUP_DIR/backup_info.txt" << EOF
QuantTradeX 系统备份信息
========================

备份时间: $(date '+%Y-%m-%d %H:%M:%S')
备份目录: $BACKUP_DIR
服务器: $(hostname)
系统: $(uname -a)

备份内容:
├── project/quanttradex_project.tar.gz  # 项目文件压缩包
├── database/                           # 数据库备份
│   ├── quanttradex_*.sql.gz           # PostgreSQL备份
│   └── redis_*.rdb                    # Redis备份
├── config/                            # 配置文件
└── backup_info.txt                    # 备份信息

项目大小: $(du -sh "$PROJECT_DIR" | cut -f1)
备份大小: $(du -sh "$BACKUP_DIR" | cut -f1)

恢复说明:
1. 解压项目文件: tar -xzf project/quanttradex_project.tar.gz -C /www/wwwroot/www.gdpp.com/
2. 恢复数据库: gunzip database/quanttradex_*.sql.gz && sudo -u postgres psql quanttradex < database/quanttradex_*.sql
3. 恢复Redis: cp database/redis_*.rdb /var/lib/redis/dump.rdb && systemctl restart redis
4. 恢复配置: cp config/* 到对应位置
5. 重启服务: systemctl restart quanttradex nginx

EOF

# 7. 创建最终压缩包
log "🗜️ 创建最终备份压缩包..."
cd "$(dirname "$BACKUP_DIR")" || error_exit "无法进入备份父目录"
BACKUP_NAME=$(basename "$BACKUP_DIR")
tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME/" || error_exit "创建压缩包失败"

# 删除临时目录
rm -rf "$BACKUP_DIR"

FINAL_BACKUP="/backup/${BACKUP_NAME}.tar.gz"
BACKUP_SIZE=$(du -sh "$FINAL_BACKUP" | cut -f1)

log "✅ 备份完成！"
log "📁 备份文件: $FINAL_BACKUP"
log "📊 备份大小: $BACKUP_SIZE"
log "📋 日志文件: $LOG_FILE"

echo ""
echo "🎉 QuantTradeX 备份成功完成！"
echo "📁 备份文件位置: $FINAL_BACKUP"
echo "📊 备份文件大小: $BACKUP_SIZE"
echo ""
echo "恢复命令示例:"
echo "tar -xzf $FINAL_BACKUP -C /backup/"
echo ""
