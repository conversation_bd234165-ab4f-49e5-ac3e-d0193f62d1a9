#!/bin/bash
# QuantTradeX 自动备份脚本
# 作者: QuantTradeX Team
# 版本: v1.0
# 创建时间: 2025-01-27

# ===========================================
# 配置区域 - 根据实际环境修改
# ===========================================

# 基础路径配置
PROJECT_DIR="/www/wwwroot/www.gdpp.com"
BACKUP_BASE_DIR="/backup/quanttradex"
SCRIPTS_DIR="/backup/scripts"
LOG_DIR="/var/log/quanttradex"

# 备份配置
BACKUP_RETENTION_DAYS=30  # 保留备份天数
REMOTE_BACKUP_ENABLED=false  # 是否启用远程备份
REMOTE_HOST="backup-server.com"
REMOTE_USER="backup"
REMOTE_PATH="/remote/backup/quanttradex"

# PostgreSQL数据库配置
DB_ENABLED=true
DB_TYPE="postgresql"
DB_HOST="localhost"
DB_USER="postgres"
DB_PASSWORD=""
DB_NAME="quanttradex"

# Redis配置
REDIS_ENABLED=true
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_DB=0

# 通知配置
EMAIL_ENABLED=false
EMAIL_TO="<EMAIL>"
WEBHOOK_ENABLED=false
WEBHOOK_URL=""

# ===========================================
# 函数定义
# ===========================================

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${LOG_DIR}/backup.log"
}

# 错误处理函数
error_exit() {
    log "❌ 错误: $1"
    send_notification "备份失败" "$1"
    exit 1
}

# 成功通知函数
success_notification() {
    log "✅ $1"
    send_notification "备份成功" "$1"
}

# 发送通知函数
send_notification() {
    local title="$1"
    local message="$2"
    
    # 邮件通知
    if [ "$EMAIL_ENABLED" = true ] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "QuantTradeX: $title" "$EMAIL_TO"
    fi
    
    # Webhook通知
    if [ "$WEBHOOK_ENABLED" = true ] && [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
             -H "Content-Type: application/json" \
             -d "{\"title\":\"$title\",\"message\":\"$message\"}" \
             >/dev/null 2>&1
    fi
}

# 检查依赖函数
check_dependencies() {
    log "🔍 检查系统依赖..."
    
    # 检查必要命令
    local commands=("tar" "gzip" "find" "cp" "mkdir")
    for cmd in "${commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            error_exit "缺少必要命令: $cmd"
        fi
    done
    
    # 检查目录权限
    if [ ! -r "$PROJECT_DIR" ]; then
        error_exit "无法读取项目目录: $PROJECT_DIR"
    fi
    
    if [ ! -w "$BACKUP_BASE_DIR" ]; then
        error_exit "无法写入备份目录: $BACKUP_BASE_DIR"
    fi
    
    log "✅ 依赖检查完成"
}

# 创建备份目录函数
create_backup_dirs() {
    local backup_name="$1"
    local backup_dir="${BACKUP_BASE_DIR}/${backup_name}"
    
    log "📁 创建备份目录结构..."
    
    mkdir -p "${backup_dir}"/{project,config,database,redis,logs,scripts} || error_exit "创建备份目录失败"
    
    echo "$backup_dir"
}

# 备份项目文件函数
backup_project_files() {
    local backup_dir="$1"
    
    log "📦 备份项目文件..."
    
    # 排除不需要备份的文件和目录
    local exclude_patterns=(
        "--exclude=*.pyc"
        "--exclude=__pycache__"
        "--exclude=.git"
        "--exclude=node_modules"
        "--exclude=*.log"
        "--exclude=venv"
        "--exclude=.env"
    )
    
    # 使用rsync进行增量备份（如果可用）
    if command -v rsync >/dev/null 2>&1; then
        # 创建目标目录
        mkdir -p "${backup_dir}/project/"
        rsync -av "${exclude_patterns[@]}" "$PROJECT_DIR/" "${backup_dir}/project/" || error_exit "项目文件备份失败"
    else
        # 使用cp作为备用方案
        cp -r "$PROJECT_DIR" "${backup_dir}/project/" || error_exit "项目文件备份失败"

        # 手动清理不需要的文件
        find "${backup_dir}/project/" -name "*.pyc" -delete 2>/dev/null || true
        find "${backup_dir}/project/" -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    fi
    
    log "✅ 项目文件备份完成"
}

# 备份配置文件函数
backup_config_files() {
    local backup_dir="$1"
    
    log "⚙️ 备份配置文件..."
    
    # Nginx配置
    if [ -f "/etc/nginx/sites-available/gdpp.com" ]; then
        cp "/etc/nginx/sites-available/gdpp.com" "${backup_dir}/config/nginx_gdpp.com.conf"
    fi
    
    # Systemd服务配置
    if [ -f "/etc/systemd/system/quanttradex.service" ]; then
        cp "/etc/systemd/system/quanttradex.service" "${backup_dir}/config/quanttradex.service"
    fi
    
    # SSL证书（如果存在）
    if [ -d "/etc/letsencrypt/live/gdpp.com" ]; then
        cp -r "/etc/letsencrypt/live/gdpp.com" "${backup_dir}/config/ssl_certificates/"
    fi
    
    # 防火墙规则
    if command -v ufw >/dev/null 2>&1; then
        ufw status verbose > "${backup_dir}/config/ufw_rules.txt"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        firewall-cmd --list-all > "${backup_dir}/config/firewall_rules.txt"
    fi
    
    log "✅ 配置文件备份完成"
}

# 备份数据库函数
backup_database() {
    local backup_dir="$1"

    if [ "$DB_ENABLED" = false ]; then
        log "⏭️ 跳过数据库备份（未启用）"
        return
    fi

    log "🗄️ 备份数据库..."

    # PostgreSQL备份
    if [ "$DB_TYPE" = "postgresql" ] && command -v pg_dump >/dev/null 2>&1; then
        local dump_file="${backup_dir}/database/quanttradex_$(date +%Y%m%d_%H%M%S).sql"

        # 使用postgres用户执行备份
        sudo -u postgres pg_dump -h "$DB_HOST" "$DB_NAME" > "$dump_file" || error_exit "PostgreSQL数据库备份失败"

        # 压缩数据库备份
        gzip "$dump_file"

        log "✅ PostgreSQL数据库备份完成"

    # MySQL/MariaDB备份
    elif [ "$DB_TYPE" = "mysql" ] && command -v mysqldump >/dev/null 2>&1; then
        local dump_file="${backup_dir}/database/quanttradex_$(date +%Y%m%d_%H%M%S).sql"

        if [ -n "$DB_PASSWORD" ]; then
            mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$dump_file" || error_exit "MySQL数据库备份失败"
        else
            mysqldump -h "$DB_HOST" -u "$DB_USER" "$DB_NAME" > "$dump_file" || error_exit "MySQL数据库备份失败"
        fi

        # 压缩数据库备份
        gzip "$dump_file"

        log "✅ MySQL数据库备份完成"
    else
        log "⚠️ 未找到对应的数据库备份命令，跳过数据库备份"
    fi
}

# 备份Redis数据函数
backup_redis() {
    local backup_dir="$1"

    if [ "$REDIS_ENABLED" = false ]; then
        log "⏭️ 跳过Redis备份（未启用）"
        return
    fi

    log "📦 备份Redis数据..."

    if command -v redis-cli >/dev/null 2>&1; then
        # 创建Redis备份目录
        mkdir -p "${backup_dir}/redis/"

        # 执行BGSAVE命令
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" BGSAVE >/dev/null

        # 等待备份完成
        sleep 2

        # 复制RDB文件
        local redis_data_dir="/var/lib/redis"
        if [ -f "${redis_data_dir}/dump.rdb" ]; then
            cp "${redis_data_dir}/dump.rdb" "${backup_dir}/redis/dump_$(date +%Y%m%d_%H%M%S).rdb"
            log "✅ Redis数据备份完成"
        else
            log "⚠️ 未找到Redis数据文件"
        fi
    else
        log "⚠️ 未找到redis-cli命令，跳过Redis备份"
    fi
}

# 备份日志文件函数
backup_logs() {
    local backup_dir="$1"
    
    log "📋 备份日志文件..."
    
    # 应用日志
    if [ -d "$LOG_DIR" ]; then
        cp -r "$LOG_DIR" "${backup_dir}/logs/app_logs/"
    fi
    
    # Nginx日志
    if [ -d "/var/log/nginx" ]; then
        mkdir -p "${backup_dir}/logs/nginx/"
        cp /var/log/nginx/gdpp.com.* "${backup_dir}/logs/nginx/" 2>/dev/null || true
    fi
    
    # 系统日志（最近7天）
    if command -v journalctl >/dev/null 2>&1; then
        journalctl --since "7 days ago" --until "now" > "${backup_dir}/logs/system.log"
    fi
    
    log "✅ 日志文件备份完成"
}

# 备份脚本文件函数
backup_scripts() {
    local backup_dir="$1"
    
    log "📜 备份脚本文件..."
    
    if [ -d "$SCRIPTS_DIR" ]; then
        cp -r "$SCRIPTS_DIR" "${backup_dir}/scripts/"
    fi
    
    # 备份crontab
    crontab -l > "${backup_dir}/scripts/crontab_backup.txt" 2>/dev/null || echo "# No crontab found" > "${backup_dir}/scripts/crontab_backup.txt"
    
    log "✅ 脚本文件备份完成"
}

# 生成备份信息函数
generate_backup_info() {
    local backup_dir="$1"
    local backup_name="$2"
    
    log "📝 生成备份信息..."
    
    local info_file="${backup_dir}/backup_info.txt"
    
    cat > "$info_file" << EOF
QuantTradeX 系统备份信息
========================

备份名称: $backup_name
备份时间: $(date '+%Y-%m-%d %H:%M:%S')
备份版本: v1.0
服务器信息: $(hostname) ($(uname -a))

目录结构:
├── project/          # 项目文件
├── config/           # 配置文件
├── database/         # 数据库备份
├── logs/             # 日志文件
├── scripts/          # 脚本文件
└── backup_info.txt   # 备份信息

环境信息:
---------
Python版本: $(python3 --version 2>/dev/null || echo "未安装")
Nginx版本: $(nginx -v 2>&1 || echo "未安装")
系统版本: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
磁盘使用: $(df -h / | awk 'NR==2 {print $5}')
内存使用: $(free -h | awk 'NR==2{printf "%.1f/%.1fGB (%.0f%%)", $3/1024/1024, $2/1024/1024, $3*100/$2}')

备份统计:
---------
项目文件大小: $(du -sh "${backup_dir}/project" | cut -f1)
配置文件数量: $(find "${backup_dir}/config" -type f | wc -l)
日志文件大小: $(du -sh "${backup_dir}/logs" | cut -f1)

恢复说明:
---------
1. 解压备份文件到目标服务器
2. 复制project目录到/www/wwwroot/gdpp.com
3. 恢复config目录中的配置文件
4. 如有数据库，恢复database目录中的SQL文件
5. 重启相关服务

联系信息:
---------
技术支持: <EMAIL>
文档地址: https://docs.quanttradex.com
EOF

    log "✅ 备份信息生成完成"
}

# 压缩备份函数
compress_backup() {
    local backup_dir="$1"
    local backup_name="$2"
    
    log "🗜️ 压缩备份文件..."
    
    cd "$BACKUP_BASE_DIR" || error_exit "无法进入备份目录"
    
    # 创建压缩包
    tar -czf "${backup_name}.tar.gz" "$backup_name/" || error_exit "压缩备份失败"
    
    # 删除临时目录
    rm -rf "$backup_name/"
    
    local compressed_size=$(du -sh "${backup_name}.tar.gz" | cut -f1)
    log "✅ 备份压缩完成，大小: $compressed_size"
    
    echo "${BACKUP_BASE_DIR}/${backup_name}.tar.gz"
}

# 清理旧备份函数
cleanup_old_backups() {
    log "🧹 清理旧备份文件..."
    
    local deleted_count=0
    while IFS= read -r -d '' file; do
        rm -f "$file"
        ((deleted_count++))
    done < <(find "$BACKUP_BASE_DIR" -name "quanttradex_backup_*.tar.gz" -mtime +$BACKUP_RETENTION_DAYS -print0)
    
    if [ $deleted_count -gt 0 ]; then
        log "✅ 清理完成，删除了 $deleted_count 个旧备份文件"
    else
        log "✅ 无需清理旧备份文件"
    fi
}

# 远程备份函数
remote_backup() {
    local backup_file="$1"
    
    if [ "$REMOTE_BACKUP_ENABLED" = false ]; then
        log "⏭️ 跳过远程备份（未启用）"
        return
    fi
    
    log "☁️ 开始远程备份..."
    
    if command -v rsync >/dev/null 2>&1; then
        rsync -av --progress "$backup_file" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/" || log "⚠️ 远程备份失败"
    elif command -v scp >/dev/null 2>&1; then
        scp "$backup_file" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/" || log "⚠️ 远程备份失败"
    else
        log "⚠️ 未找到rsync或scp命令，跳过远程备份"
        return
    fi
    
    log "✅ 远程备份完成"
}

# ===========================================
# 主函数
# ===========================================

main() {
    local start_time=$(date +%s)
    local backup_name="quanttradex_backup_$(date +%Y%m%d_%H%M%S)"
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    log "🚀 开始 QuantTradeX 系统备份..."
    log "备份名称: $backup_name"
    
    # 检查依赖
    check_dependencies
    
    # 创建备份目录
    local backup_dir=$(create_backup_dirs "$backup_name")

    # 执行各项备份任务
    backup_project_files "$backup_dir"
    backup_config_files "$backup_dir"
    backup_database "$backup_dir"
    backup_redis "$backup_dir"
    backup_logs "$backup_dir"
    backup_scripts "$backup_dir"
    
    # 生成备份信息
    generate_backup_info "$backup_dir" "$backup_name"
    
    # 压缩备份
    local backup_file=$(compress_backup "$backup_dir" "$backup_name")
    
    # 远程备份
    remote_backup "$backup_file"
    
    # 清理旧备份
    cleanup_old_backups
    
    # 计算耗时
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    success_notification "QuantTradeX 系统备份完成！耗时: ${duration}秒，备份文件: $backup_file"
}

# ===========================================
# 脚本入口
# ===========================================

# 检查是否以root权限运行
if [ "$EUID" -eq 0 ]; then
    log "⚠️ 警告: 以root权限运行备份脚本"
fi

# 设置错误处理
set -e
trap 'error_exit "脚本执行过程中发生未知错误"' ERR

# 执行主函数
main "$@"

log "🎉 备份脚本执行完成！"
