#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试会员状态同步修复效果
特别测试 /strategy-editor 页面的会员状态同步
"""

import requests
import time

def test_strategy_editor_sync():
    """测试策略编辑器页面的会员状态同步"""
    base_url = "http://127.0.0.1:5001"  # 使用5001端口

    print("🔍 测试策略编辑器页面会员状态同步")
    print("=" * 60)

    # 创建会话
    session = requests.Session()

    # 1. 测试登录
    print("1. 测试管理员登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        success = result.get('success', False)
        print(f"   登录结果: {'✅ 成功' if success else '❌ 失败'}")
        if success:
            user = result.get('user', {})
            print(f"   用户: {user.get('username')} ({user.get('full_name')})")
            print(f"   角色: {user.get('role')}")
            print(f"   会员类型: {user.get('membership_type', 'basic')}")
            print(f"   VIP状态: {'是' if user.get('is_premium') else '否'}")
        else:
            print(f"   ❌ 登录失败: {result.get('message')}")
            return False
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return False

    # 2. 测试策略编辑器页面访问
    print("\n2. 测试策略编辑器页面访问...")
    try:
        response = session.get(f"{base_url}/strategy-editor", timeout=10)
        if response.status_code == 200:
            print("   ✅ 策略编辑器页面访问成功")

            # 检查页面是否包含用户认证脚本
            content = response.text
            if 'user-auth.js' in content:
                print("   ✅ 页面包含用户认证脚本")
            else:
                print("   ❌ 页面缺少用户认证脚本")
                return False

            # 检查页面是否包含会员功能相关元素
            if 'checkLoginStatus' in content:
                print("   ✅ 页面包含登录状态检查")
            else:
                print("   ❌ 页面缺少登录状态检查")
                return False

            # 检查导航栏是否统一
            if 'data-feature="stock-prediction"' in content:
                print("   ✅ 导航栏包含AI股票预测入口")
            else:
                print("   ❌ 导航栏缺少AI股票预测入口")
                return False

        else:
            print(f"   ❌ 策略编辑器页面访问失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"   ❌ 策略编辑器页面访问异常: {e}")
        return False

    # 3. 测试用户状态API在策略编辑器页面的一致性
    print("\n3. 测试用户状态API一致性...")
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        if result.get('success') and result.get('authenticated'):
            user = result.get('user', {})
            print("   ✅ 用户状态API正常")
            print(f"   用户名: {user.get('username')}")
            print(f"   角色: {user.get('role')}")
            print(f"   会员类型: {user.get('membership_type', 'basic')}")
            print(f"   VIP状态: {'是' if user.get('is_premium') else '否'}")
        else:
            print("   ❌ 用户状态API返回未认证")
            return False
    except Exception as e:
        print(f"   ❌ 用户状态API异常: {e}")
        return False

    # 4. 测试其他页面的状态同步
    print("\n4. 测试其他页面的状态同步...")
    test_pages = [
        ('/dashboard', '仪表板'),
        ('/strategies', '策略市场'),
        ('/backtest', '回测系统'),
        ('/membership', '会员中心')
    ]

    all_pages_ok = True
    for url, name in test_pages:
        try:
            response = session.get(f"{base_url}{url}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {name}页面访问正常")
            elif response.status_code in [302, 301]:
                print(f"   ✅ {name}页面正确重定向")
            else:
                print(f"   ⚠️  {name}页面状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}页面访问异常: {e}")
            all_pages_ok = False

    # 5. 测试会话持久性
    print("\n5. 测试会话持久性...")
    print("   等待5秒...")
    time.sleep(5)

    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        if result.get('success') and result.get('authenticated'):
            print("   ✅ 5秒后会话仍然有效")
        else:
            print("   ❌ 会话在5秒后失效")
            return False
    except Exception as e:
        print(f"   ❌ 会话持久性检查异常: {e}")
        return False

    print("\n✅ 策略编辑器页面会员状态同步测试完成！")
    return True

def test_navigation_consistency():
    """测试导航栏一致性"""
    base_url = "http://127.0.0.1:5001"

    print("\n🔍 测试导航栏一致性")
    print("=" * 60)

    # 创建会话
    session = requests.Session()

    # 先登录
    login_data = {"username": "admin", "password": "admin123"}
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        if not result.get('success'):
            print("   ❌ 登录失败，无法测试导航栏")
            return False
        print("   ✅ 登录成功")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return False

    # 测试有导航栏的页面
    pages_with_navbar = [
        ('/', '首页'),
        ('/strategy-editor', '策略编辑器')
    ]

    # 测试专门页面（无导航栏设计）
    special_pages = [
        ('/stock-prediction', 'AI股票预测'),
        ('/membership', '会员中心')
    ]

    navigation_elements = [
        'fas fa-home',  # 首页图标
        'fas fa-tachometer-alt',  # 仪表板图标
        'fas fa-store',  # 策略市场图标
        'fas fa-code',  # 策略开发图标
        'fas fa-brain',  # AI预测图标
        'fas fa-history',  # 回测图标
        'fas fa-comments'  # 论坛图标
    ]

    all_consistent = True

    # 测试有导航栏的页面
    print("   测试有导航栏的页面:")
    for url, name in pages_with_navbar:
        try:
            response = session.get(f"{base_url}{url}", timeout=5)
            if response.status_code == 200:
                content = response.text
                missing_elements = []

                for element in navigation_elements:
                    if element not in content:
                        missing_elements.append(element)

                if not missing_elements:
                    print(f"     ✅ {name}页面导航栏完整")
                else:
                    print(f"     ⚠️  {name}页面缺少导航元素: {missing_elements}")
                    all_consistent = False
            else:
                print(f"     ❌ {name}页面访问失败 (状态码: {response.status_code})")
                all_consistent = False
        except Exception as e:
            print(f"     ❌ {name}页面访问异常: {e}")
            all_consistent = False

    # 测试专门页面（这些页面设计为无导航栏）
    print("   测试专门页面（无导航栏设计）:")
    for url, name in special_pages:
        try:
            response = session.get(f"{base_url}{url}", timeout=5)
            if response.status_code == 200:
                print(f"     ✅ {name}页面访问正常（专门设计）")
            else:
                print(f"     ❌ {name}页面访问失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"     ❌ {name}页面访问异常: {e}")

    if all_consistent:
        print("\n✅ 导航栏一致性测试通过！")
        print("   - 有导航栏的页面保持一致")
        print("   - 专门页面采用独立设计")
    else:
        print("\n⚠️  有导航栏的页面存在不一致问题")

    return all_consistent

def main():
    """主函数"""
    print("🎯 QuantTradeX 会员状态同步修复验证")
    print("请确保应用正在运行在 http://127.0.0.1:5001")
    print()

    # 测试策略编辑器页面同步
    strategy_editor_test = test_strategy_editor_sync()

    # 测试导航栏一致性
    navigation_test = test_navigation_consistency()

    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   策略编辑器状态同步: {'✅ 通过' if strategy_editor_test else '❌ 失败'}")
    print(f"   导航栏一致性: {'✅ 通过' if navigation_test else '❌ 失败'}")

    if strategy_editor_test and navigation_test:
        print("\n🎉 所有测试通过！修复成功。")
        print("\n✨ 修复内容:")
        print("   - ✅ 策略编辑器页面添加了用户认证脚本")
        print("   - ✅ 策略编辑器页面会员状态正常同步")
        print("   - ✅ 导航栏设计统一，包含图标和VIP标识")
        print("   - ✅ 所有页面的会员状态检查一致")
        print("   - ✅ 会话持久性正常工作")
    else:
        print("\n⚠️  部分问题仍需修复。")

if __name__ == "__main__":
    main()
